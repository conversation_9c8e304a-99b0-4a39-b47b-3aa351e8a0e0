const fs = require('fs');

class LGDGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }

  async fetchWithTimeout(resource, options = {}, timeout = 8000) {
    const controller = new AbortController();
    const id = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(resource, {
        ...options,
        signal: controller.signal  // Pass the signal to fetch
      });
      clearTimeout(id); // Clear the timeout if the request completes in time
      return response;
    } catch (error) {
      clearTimeout(id); // Also clear timeout on other fetch errors
      if (error.name === 'AbortError') {
        // This error is specifically from controller.abort()
        throw new Error('Request timed out');
      }
      throw error; // Re-throw other errors (e.g., network errors)
    }
  }

  async generateResponse(model, prompt, config, maxRetries = 3) {
    const timeoutMs = 180000; // 3 minutes timeout

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempt ${attempt}/${maxRetries} for model ${model}`);

        const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
        const queryParams = `key=${process.env.GEMINI_API_KEY}`;
        const fetchUrl = `${requestUrl}?${queryParams}`;

        const requestBody = {
          system_instruction: { parts: [{ text: config.systemInstruction }] },
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: config.temperature,
            responseMimeType: config.responseMimeType
          }
        };

        // Create AbortController for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
        }, timeoutMs);

        const fetchOptions = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'LGD-Evaluation-Service/1.0'
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal
        };

        try {
          const response = await fetch(fetchUrl, fetchOptions);
          clearTimeout(timeoutId);

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
          }

          const data = await response.json();

          if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0]) {
            throw new Error('Invalid response structure from Gemini API');
          }

          return data.candidates[0].content.parts[0].text;
        } catch (fetchError) {
          clearTimeout(timeoutId);
          throw fetchError;
        }

      } catch (error) {
        console.error(`Attempt ${attempt} failed:`, error.message);

        // Check if it's a timeout or network error that we should retry
        const isRetryableError =
          error.name === 'AbortError' ||
          error.code === 'UND_ERR_HEADERS_TIMEOUT' ||
          error.code === 'UND_ERR_CONNECT_TIMEOUT' ||
          error.message.includes('timeout') ||
          error.message.includes('ECONNRESET') ||
          error.message.includes('ENOTFOUND') ||
          (error.message.includes('HTTP error') && error.message.includes('5'));

        if (attempt === maxRetries || !isRetryableError) {
          console.error('Final attempt failed or non-retryable error:', error);
          throw new Error(`Failed to generate response from Gemini after ${attempt} attempts: ${error.message}`);
        }

        // Exponential backoff: wait 2^attempt seconds before retry
        const waitTime = Math.pow(2, attempt) * 1000;
        console.log(`Waiting ${waitTime}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  async runLGDPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies } = input;

      // Step 1: Run LGD analysis prompt with transcript and competencies
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{lgd_competencies}}',
        competencies
      )

      const analysisUserPrompt = `
        Here is the transcripts:
        ${transcript}
      `

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log(`Current time: ${new Date().toISOString()}`)
      console.log('Running LGD analysis prompt...');
      // console.log("UserPrompt:");
      // console.log(analysisUserPrompt);
      // console.log("Config:");
      // console.log(analysisConfig);
      // write user prompt to tmp/user_prompt.txt
      // and config to tmp/config.txt
      // fs.writeFileSync('tmp/user_prompt.txt', analysisUserPrompt);
      // fs.writeFileSync('tmp/config.txt', JSON.stringify(analysisConfig));
      const analysisOutput = await this.generateResponse(
        'gemini-2.5-pro-preview-05-06',
        analysisUserPrompt,
        analysisConfig
      );

      // Step 2: Run formatting prompt with analysis output
      const formattingUserPrompt = `
        You need to format this text content to JSON format
        ${analysisOutput}
      `

      const formattingConfig = {
        temperature: 0,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      console.log('Running LGD formatting prompt...');
      const finalOutput = await this.generateResponse(
        'gemini-2.5-flash-preview-05-20',
        formattingUserPrompt,
        formattingConfig
      );

      return {
        step1: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput
        },
        step2: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: finalOutput
        },
        finalOutput
      };
    } catch (error) {
      console.error('Error in LGD prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new LGDGeminiService();
